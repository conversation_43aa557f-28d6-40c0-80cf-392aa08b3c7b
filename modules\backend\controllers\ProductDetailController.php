<?php

namespace app\modules\backend\controllers;

use app\common\models\Colors;
use app\common\models\Design;
use app\common\models\Orders;
use app\common\models\OrdersDetail;
use app\common\models\Product;
use app\common\models\ProductSerial;
use app\common\models\Sizes;
use app\common\models\Storage;
use app\common\models\StorageBalance;
use app\common\models\User;
use Yii;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

/**
 * ProductDetailController отвечает за отображение деталей продукта (серий)
 */
class ProductDetailController extends BaseController
{
    /**
     * Отображает историю продаж серийного номера
     *
     * @param string $serial Серийный номер продукта
     * @return string
     * @throws NotFoundHttpException если серийный номер не найден
     */
    public function actionSerialHistory($serial)
    {
        // Находим серийный номер в базе
        $productSerial = ProductSerial::find()
            ->where(['serial' => $serial, 'deleted_at' => null])
            ->one();

        if (!$productSerial) {
            throw new NotFoundHttpException(Yii::t('app', 'The requested serial number was not found.'));
        }

        // Получаем изначальный размер продукта
        $originalSize = Yii::$app->db->createCommand("
            SELECT sbh.size as original_size
            FROM storage_balance_history sbh
            LEFT JOIN product_serial ps ON ps.id = sbh.product_serial_id
            WHERE ps.serial = :serial
            AND sbh.deleted_at IS NULL
            AND sbh.status = 1
            AND sbh.size > 0
            ORDER BY sbh.id ASC
            LIMIT 1
        ")
        ->bindValue(':serial', $serial)
        ->queryScalar();

        // Получаем историю продаж этого серийного номера
        $salesHistory = Yii::$app->db->createCommand("
            SELECT
                od.id,
                od.serial,
                od.size_meter,
                od.created_at,
                u.full_name as seller_name
            FROM order_detail od
            LEFT JOIN orders o ON od.order_id = o.id
            LEFT JOIN users u ON o.user_id = u.id
            WHERE od.serial = :serial
            AND od.deleted_at IS NULL
            ORDER BY od.created_at DESC
        ")
        ->bindValue(':serial', $serial)
        ->queryAll();


        return $this->render('serial-history', [
            'serial' => $serial,
            'salesHistory' => $salesHistory,
            'originalSize' => $originalSize ?: 0,
        ]);
    }
    /**
     * Отображает детали продукта (серии)
     *
     * @param int $product_id ID продукта
     * @param int $color_id ID цвета
     * @param int $size_id ID размера
     * @param int $design_id ID дизайна
     * @param int $storage_id ID склада
     * @return string
     * @throws NotFoundHttpException если продукт не найден
     */
    public function actionView($product_id, $color_id, $size_id, $design_id, $storage_id)
    {
        // Находим баланс на складе для указанного продукта
        $storageBalance = StorageBalance::find()
            ->where([
                'product_id' => $product_id,
                'color_id' => $color_id,
                'size_id' => $size_id,
                'design_id' => $design_id,
                'storage_id' => $storage_id,
                'deleted_at' => null
            ])
            ->one();

        if (!$storageBalance) {
            throw new NotFoundHttpException(Yii::t('app', 'The requested product was not found.'));
        }

        // Получаем информацию о продукте
        $product = Product::findOne($product_id);
        $color = Colors::findOne($color_id);
        $size = Sizes::findOne($size_id);
        $design = Design::findOne($design_id);
        $storage = Storage::findOne($storage_id);

        // Получаем серии для данного продукта с их размерами из истории баланса
        $serials = Yii::$app->db->createCommand("
            SELECT
                ps.id,
                ps.serial,
                COALESCE(SUM(sbh.size), 0) as size
            FROM product_serial ps
            LEFT JOIN storage_balance_history sbh ON sbh.product_serial_id = ps.id
            WHERE ps.storage_balance_id = :storage_balance_id
            AND ps.deleted_at IS NULL
            AND  sbh.deleted_at IS NULL
            AND (sbh.size > 0 OR sbh.size IS NULL)
            AND (sbh.status = 1 OR sbh.status IS NULL)
            GROUP BY ps.id, ps.serial
        ")
        ->bindValue(':storage_balance_id', $storageBalance->id)
        ->queryAll();

        // Рассчитываем общий размер из истории баланса
        $totalSize = Yii::$app->db->createCommand("
            SELECT COALESCE(SUM(sbh.size), 0) as total_size
            FROM storage_balance_history sbh
            JOIN product_serial ps ON sbh.product_serial_id = ps.id
            WHERE ps.storage_balance_id = :storage_balance_id
            AND sbh.deleted_at IS NULL
            AND sbh.size > 0
            AND sbh.status = 1
        ")
        ->bindValue(':storage_balance_id', $storageBalance->id)
        ->queryScalar();

        // Обновляем объект storageBalance с новым значением total_size
        $storageBalance->total_size = $totalSize;

        return $this->render('view', [
            'product' => $product,
            'color' => $color,
            'size' => $size,
            'design' => $design,
            'storage' => $storage,
            'storageBalance' => $storageBalance,
            'serials' => $serials,
        ]);
    }

    /**
     * Обновляет серийный номер продукта
     *
     * @return array JSON-ответ
     */
    public function actionUpdateSerial()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isAjax || !Yii::$app->request->isPost) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Invalid request')
            ];
        }

        $currentSerial = Yii::$app->request->post('current_serial');
        $newSerial = Yii::$app->request->post('new_serial');

        if (empty($currentSerial) || empty($newSerial)) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Serial number cannot be empty')
            ];
        }

        // Проверяем, существует ли уже такой серийный номер
        $existingSerial = ProductSerial::findOne(['serial' => $newSerial, 'deleted_at' => null]);
        if ($existingSerial && $existingSerial->serial !== $currentSerial) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Serial number already exists')
            ];
        }

        // Находим текущий серийный номер
        $productSerial = ProductSerial::findOne(['serial' => $currentSerial, 'deleted_at' => null]);
        if (!$productSerial) {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Serial number not found')
            ];
        }

        // Обновляем серийный номер
        $productSerial->serial = $newSerial;

        if ($productSerial->save()) {
            return [
                'status' => 'success',
                'message' => Yii::t('app', 'Serial number updated successfully')
            ];
        } else {
            return [
                'status' => 'error',
                'message' => Yii::t('app', 'Failed to update serial number')
            ];
        }
    }
}
