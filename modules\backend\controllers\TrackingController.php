<?php

namespace app\modules\backend\controllers;

use app\common\models\Orders;
use Yii;

class TrackingController extends BaseController
{
    public function actionIndex()
{
    $request = Yii::$app->request;
    $startDate = $request->get('startDate', date('Y-m-d'));
    $endDate = $request->get('endDate', date('Y-m-d'));
    $seller_id = $request->get('seller_id');
    $cashbox_id = $request->get('cashbox_id');
    $page = (int)$request->get('page', 1);
    $pageSize = (int)$request->get('pageSize', 30);    $user = Yii::$app->user->identity;
    $isManager = Yii::$app->user->can('manager') && !Yii::$app->user->can('admin');
    $isAdmin = Yii::$app->user->can('admin');

    // Убедимся, что pageSize не меньше 1
    if ($pageSize < 1) {
        $pageSize = 30;
    }

    $conditions = ['o.status IN (:status_accepted, :status_returned, :status_completed)'];
    $params = [
        ':status_accepted' => Orders::STATUS_ACCEPTED,
        ':status_returned' => Orders::STATUS_RETURNED,
        ':status_completed' => Orders::STATUS_COMPLETED,
        ':startDate' => $startDate . ' 00:00:00',
        ':endDate' => $endDate . ' 23:59:59',
    ];    // Добавляем параметр storageId только если пользователь менеджер
    if ($isManager) {
        $params[':storageId'] = $user->storage->id;
    }

    // Фильтр по дате принятия заказа
    $conditions[] = 'o.accepted_date BETWEEN :startDate AND :endDate';


    if ($seller_id && $seller_id !== 'null') {
        $conditions[] = 'o.add_user_id = :seller_id';
        $params[':seller_id'] = (int)$seller_id;
    }

    if ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '') {
        $conditions[] = 'o.cashbox_id = :cashbox_id';
        $params[':cashbox_id'] = (int)$cashbox_id;
    }    // Добавляем условие по storage_id только если пользователь менеджер
    if ($isManager) {
        $conditions[] = 'o.storage_id = :storageId';
    }

    $whereClause = implode(' AND ', $conditions);

    // Добавляем дополнительные условия для фильтрации по дате оплаты, изменения статуса и возвратов в основном запросе

    // Запрос для подсчёта общего количества записей (включая возвраты как отдельные записи)
    $countSql = "
        WITH filtered_orders AS (
            SELECT o.id, 'created' as display_reason
            FROM orders o
            WHERE {$whereClause}
            UNION
            SELECT o.id, 'payment' as display_reason
            FROM orders o
            WHERE o.payment_date IS NOT NULL
              AND o.payment_date BETWEEN :startDate AND :endDate
              AND o.status IN (:status_accepted, :status_returned, :status_completed)
            UNION
            SELECT o.id, 'status_change' as display_reason
            FROM orders o
            WHERE o.last_status_change_date IS NOT NULL
              AND o.last_status_change_date BETWEEN :startDate AND :endDate
              AND o.status IN (:status_accepted, :status_returned, :status_completed)
        ),
        orders_with_returns_other_day AS (
            SELECT DISTINCT o.id
            FROM orders o
            JOIN order_returns r ON r.order_id = o.id
            WHERE r.status = 1
            AND r.created_at BETWEEN :startDate AND :endDate
            AND o.accepted_date NOT BETWEEN :startDate AND :endDate
        ),
        return_orders AS (
            SELECT DISTINCT
                o.id as order_id,
                r.created_at as return_created_at
            FROM orders o
            JOIN order_returns r ON r.order_id = o.id
            WHERE r.status = 1
            GROUP BY o.id, r.created_at
        ),
        all_records AS (
            -- Основные заказы
            SELECT o.id, 'original' as record_type
            FROM orders o
            INNER JOIN filtered_orders fo ON fo.id = o.id
            LEFT JOIN orders_with_returns_other_day owr ON owr.id = o.id
            WHERE owr.id IS NULL

            UNION ALL

            -- Возвраты заказов как отдельные записи
            SELECT ro.order_id as id, 'return' as record_type
            FROM return_orders ro
            WHERE ro.return_created_at BETWEEN :startDate AND :endDate
        )
        SELECT COUNT(*) FROM all_records
    ";

    // Явно приводим результат к integer
    $totalCount = (int)Yii::$app->db->createCommand($countSql, $params)->queryScalar();

    // Основной запрос с фильтрацией возвратов в CTE
    $sql = "WITH filtered_orders AS (
            SELECT o.id, 'created' as display_reason
            FROM orders o
            WHERE {$whereClause}
            UNION
            SELECT o.id, 'payment' as display_reason
            FROM orders o
            WHERE o.payment_date IS NOT NULL
              AND o.payment_date BETWEEN :startDate AND :endDate
              AND o.status IN (:status_accepted, :status_returned, :status_completed)
              " . (!$isAdmin ? "AND o.storage_id = :storageId" : "") . "
              " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND o.cashbox_id = :cashbox_id" : "") . "
              " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND o.add_user_id = :seller_id" : "") . "
            UNION
            SELECT o.id, 'status_change' as display_reason
            FROM orders o
            WHERE o.last_status_change_date IS NOT NULL
              AND o.last_status_change_date BETWEEN :startDate AND :endDate
              AND o.status IN (:status_accepted, :status_returned, :status_completed)
              " . (!$isAdmin ? "AND o.storage_id = :storageId" : "") . "
              " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND o.cashbox_id = :cashbox_id" : "") . "
              " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND o.add_user_id = :seller_id" : "") . "
        ),
        filtered_orders_with_reason AS (
            SELECT id,
                   CASE
                       WHEN MAX(display_reason) = 'payment' THEN '" . Yii::t('app', 'payment_reason') . "'
                       WHEN MAX(display_reason) = 'status_change' THEN '" . Yii::t('app', 'status_change') . "'
                       ELSE '" . Yii::t('app', 'created') . "'
                   END as display_reason
            FROM filtered_orders
            GROUP BY id
        ),
        returns AS (
            SELECT
                r.order_id,
                r.created_at as return_created_at,
                COUNT(DISTINCT r.order_detail_id) as returned_details_count,
                SUM(COALESCE(r.original_benefit, 0)) as returned_benefit,
                COUNT(*) as has_returns,
                SUM(r.return_amount) as total_return_amount
            FROM order_returns r
            JOIN orders o ON o.id = r.order_id
            WHERE r.status = 1
            " . (!$isAdmin ? "AND o.storage_id = :storageId" : "") . "
            " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND o.cashbox_id = :cashbox_id" : "") . "
            " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND o.add_user_id = :seller_id" : "") . "
            GROUP BY r.order_id, r.created_at
        ),
        return_orders AS (
            SELECT DISTINCT
                o.id as order_id,
                r.created_at as return_created_at,
                SUM(r.return_amount) as return_amount,
                SUM(COALESCE(r.original_benefit, 0)) as return_benefit
            FROM orders o
            JOIN order_returns r ON r.order_id = o.id
            WHERE r.status = 1
            " . (!$isAdmin ? "AND o.storage_id = :storageId" : "") . "
            " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND o.cashbox_id = :cashbox_id" : "") . "
            " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND o.add_user_id = :seller_id" : "") . "
            GROUP BY o.id, r.created_at
        ),
        order_detail_counts AS (
            SELECT
                od.order_id,
                COUNT(DISTINCT od.id) as total_details_count
            FROM order_detail od
            GROUP BY od.order_id
        ),
        order_products AS (
            SELECT
                od.order_id,
                json_agg(
                    json_build_object(
                        'name', p.name,
                        'color', co.name,
                        'design_code', d.design_code,
                        'width', s.width,
                        'height', s.height,
                        'size_meter', od.size_meter,
                        'sell_price', od.sell_price,
                        'price', od.price,
                        'order_detail_id', od.id,
                        'has_return', CASE WHEN EXISTS (
                            SELECT 1 FROM order_returns r
                            WHERE r.order_detail_id = od.id AND r.status = 1
                        ) THEN 1 ELSE 0 END,
                        'return_date', CASE WHEN EXISTS (
                            SELECT 1 FROM order_returns r
                            WHERE r.order_detail_id = od.id AND r.status = 1
                        ) THEN (
                            SELECT r.created_at
                            FROM order_returns r
                            WHERE r.order_detail_id = od.id AND r.status = 1
                            LIMIT 1
                        ) ELSE NULL END
                    )
                ) as products
            FROM order_detail od
            LEFT JOIN products p ON p.id = od.product_id
            LEFT JOIN design d ON d.id = od.design_id
            LEFT JOIN sizes s ON s.id = od.size_id
            LEFT JOIN colors co ON co.id = od.color_id
            GROUP BY od.order_id
        ),
        orders_with_returns_other_day AS (
            SELECT DISTINCT o.id
            FROM orders o
            JOIN order_returns r ON r.order_id = o.id
            WHERE r.status = 1
            AND r.created_at BETWEEN :startDate AND :endDate
            AND o.accepted_date NOT BETWEEN :startDate AND :endDate
            " . (!$isAdmin ? "AND o.storage_id = :storageId" : "") . "
            " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND o.cashbox_id = :cashbox_id" : "") . "
            " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND o.add_user_id = :seller_id" : "") . "
        )
        -- Основные заказы
        SELECT
            o.id,
            c.full_name AS client_name,
            u.full_name AS seller_name,
            NULL AS return_created_at,
            o.created_at,
            o.accepted_date,
            au.full_name AS accepted_seller_name,
            o.accepted_date,
            cb.name AS cashbox_name,
            cur.name AS currency_name,
            o.pay_status,
            o.type,
            o.total_sell_price,
            o.benefit,
            o.benefit * COALESCE(cc.course, 0) as benefit_som,
            cc.course as currency_course,
            CASE WHEN EXISTS (SELECT 1 FROM order_returns r WHERE r.order_id = o.id AND r.status = 1) THEN 1 ELSE 0 END as has_returns,
            0 as returned_benefit,
            0 as returned_details_count,
            COALESCE(od_count.total_details_count, 0) as total_details_count,
            s.name AS storage_name,
            o.benefit * COALESCE(cc.course, 0) as new_benefit_som,
            0 as new_total_sell_price,
            op.products,
            st.name as storage,
            o.comment,
            c.phone_number,
            CASE
                WHEN fo.display_reason = '" . Yii::t('app', 'payment_reason') . "' THEN
                    (SELECT COALESCE(SUM(
                        CASE
                            WHEN ph.currency_id = 1 THEN ph.sum * (
                                SELECT course FROM currency_course
                                WHERE currency_id = 2
                                AND ph.created_at >= start_date
                                AND ph.created_at < end_date
                                AND end_date = '9999-12-31'
                                ORDER BY start_date DESC LIMIT 1
                            )
                            ELSE ph.sum
                        END
                     ), 0) FROM pays_history ph
                     WHERE ph.order_id = o.id
                     AND ph.created_at BETWEEN :startDate AND :endDate
                     AND ph.deleted_at IS NULL
                     " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND ph.cashbox_id = :cashbox_id" : "") . "
                     " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND o.add_user_id = :seller_id" : "") . ")
                ELSE o.paid_sum
            END as paid_sum,
            fo.display_reason as display_reason,
            'original' as record_type,
            0 as sort_order
        FROM orders o
        INNER JOIN filtered_orders_with_reason fo ON fo.id = o.id
        LEFT JOIN orders_with_returns_other_day owr ON owr.id = o.id
        LEFT JOIN order_detail_counts od_count ON od_count.order_id = o.id
        LEFT JOIN order_products op ON op.order_id = o.id
        LEFT JOIN clients c ON c.id = o.client_id
        LEFT JOIN users u ON u.id = o.add_user_id
        LEFT JOIN users au ON au.id = o.accepted_user_id
        LEFT JOIN cashbox cb ON cb.id = o.cashbox_id
        LEFT JOIN storage st ON st.id = cb.storage_id
        LEFT JOIN storage s ON s.id = o.storage_id
        LEFT JOIN currency cur ON cur.id = o.currency_id
        LEFT JOIN LATERAL (
            SELECT course, start_date, end_date
            FROM currency_course
            WHERE currency_id = 2
            AND o.accepted_date >= start_date AND o.accepted_date < end_date
            AND end_date = '9999-12-31'
            ORDER BY start_date DESC
            LIMIT 1
        ) cc ON true
        WHERE owr.id IS NULL -- Исключаем заказы с возвратами в выбранном периоде, если сами заказы созданы в другой день

        UNION ALL

        -- Возвраты заказов
        SELECT
            o.id,
            c.full_name AS client_name,
            u.full_name AS seller_name,
            ro.return_created_at,
            o.created_at,
            o.accepted_date,
            au.full_name AS accepted_seller_name,
            o.accepted_date,
            cb.name AS cashbox_name,
            cur.name AS currency_name,
            o.pay_status,
            2 as type, -- Orders::TYPE_RETURN
            -ro.return_amount as total_sell_price,
            -ro.return_benefit as benefit,
            -ro.return_benefit * COALESCE(cc.course, 0) as benefit_som,
            cc.course as currency_course,
            1 as has_returns,
            ro.return_benefit as returned_benefit,
            (SELECT COUNT(*) FROM order_returns r WHERE r.order_id = o.id AND r.status = 1) as returned_details_count,
            COALESCE(od_count.total_details_count, 0) as total_details_count,
            s.name AS storage_name,
            -ro.return_benefit * COALESCE(cc.course, 0) as new_benefit_som,
            -ro.return_amount as new_total_sell_price,
            (SELECT json_agg(
                json_build_object(
                    'name', p.name,
                    'color', co.name,
                    'design_code', d.design_code,
                    'width', s2.width,
                    'height', s2.height,
                    'size_meter', od.size_meter,
                    'sell_price', -od.sell_price, -- Отрицательное значение для возвратов
                    'price', od.price,
                    'order_detail_id', od.id,
                    'has_return', 1,
                    'return_date', r.created_at
                )
            )
            FROM order_returns r
            JOIN order_detail od ON r.order_detail_id = od.id
            LEFT JOIN products p ON p.id = od.product_id
            LEFT JOIN design d ON d.id = od.design_id
            LEFT JOIN sizes s2 ON s2.id = od.size_id
            LEFT JOIN colors co ON co.id = od.color_id
            WHERE r.order_id = o.id AND r.status = 1 AND r.created_at = ro.return_created_at
            ) as products,
            st.name as storage,
            o.comment,
            c.phone_number,
            -ro.return_amount as paid_sum,
            'returned' as display_reason,
            'return' as record_type,
            1 as sort_order
        FROM orders o
        JOIN return_orders ro ON ro.order_id = o.id
        LEFT JOIN order_detail_counts od_count ON od_count.order_id = o.id
        LEFT JOIN clients c ON c.id = o.client_id
        LEFT JOIN users u ON u.id = o.add_user_id
        LEFT JOIN users au ON au.id = o.accepted_user_id
        LEFT JOIN cashbox cb ON cb.id = o.cashbox_id
        LEFT JOIN storage st ON st.id = cb.storage_id
        LEFT JOIN storage s ON s.id = o.storage_id
        LEFT JOIN currency cur ON cur.id = o.currency_id
        LEFT JOIN LATERAL (
            SELECT course, start_date, end_date
            FROM currency_course
            WHERE currency_id = 2
            AND ro.return_created_at >= start_date AND ro.return_created_at < end_date
            AND end_date = '9999-12-31'
            ORDER BY start_date DESC
            LIMIT 1
        ) cc ON true
        WHERE ro.return_created_at BETWEEN :startDate AND :endDate
        " . (!$isAdmin ? "AND o.storage_id = :storageId" : "") . "
        " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND o.cashbox_id = :cashbox_id" : "") . "
        " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND o.add_user_id = :seller_id" : "") . "

        ORDER BY id DESC, sort_order
        LIMIT :limit OFFSET :offset
    ";

    // Копия параметров для основного запроса с пагинацией
    $mainParams = $params;
    $mainParams[':limit'] = (int)$pageSize;
    $mainParams[':offset'] = (int)(($page - 1) * $pageSize);

    $detailedOrders = Yii::$app->db->createCommand($sql, $mainParams)->queryAll();

    // Применяем кастомное округление к benefit_som для каждой записи
    foreach ($detailedOrders as &$order) {
        if (isset($order['benefit_som'])) {
            $originalValue = $order['benefit_som'];
            $benefitSom = floatval($originalValue);


            // Кастомное округление: если остаток от деления на 1000 превышает 500 – округляем вверх,
            // иначе – округляем вниз (работает только для неотрицательных значений).
            // Сначала округляем до целого, чтобы избежать проблем с точностью
            $intBenefitSom = round($benefitSom);
            $remainder = abs($intBenefitSom) % 1000;
            $roundedBenefitSom = ($intBenefitSom - ($intBenefitSom >= 0 ? $remainder : -$remainder)) + (($remainder > 500) ? ($intBenefitSom >= 0 ? 1000 : -1000) : 0);

            // Используем round() для избежания предупреждений о потере точности
            $order['benefit_som'] = round($roundedBenefitSom);

        }
    }
    unset($order); // Освобождаем ссылку

    // Запрос для подсчета итоговых сумм за весь период (без LIMIT и OFFSET)
    $totalsSql = "WITH filtered_orders AS (
        SELECT o.id, 'created' as display_reason
        FROM orders o
        WHERE {$whereClause}
        UNION
        SELECT o.id, 'payment' as display_reason
        FROM orders o
        WHERE o.payment_date IS NOT NULL
          AND o.payment_date BETWEEN :startDate AND :endDate
          AND o.status IN (:status_accepted, :status_returned, :status_completed)
          " . (!$isAdmin ? "AND o.storage_id = :storageId" : "") . "
          " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND o.cashbox_id = :cashbox_id" : "") . "
          " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND o.add_user_id = :seller_id" : "") . "
        UNION
        SELECT o.id, 'status_change' as display_reason
        FROM orders o
        WHERE o.last_status_change_date IS NOT NULL
          AND o.last_status_change_date BETWEEN :startDate AND :endDate
          AND o.status IN (:status_accepted, :status_returned, :status_completed)
          " . (!$isAdmin ? "AND o.storage_id = :storageId" : "") . "
          " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND o.cashbox_id = :cashbox_id" : "") . "
          " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND o.add_user_id = :seller_id" : "") . "
    ),
    filtered_orders_with_reason AS (
        SELECT id,
            CASE
                WHEN MIN(display_reason) = 'created' THEN 'created'
                WHEN MIN(display_reason) = 'payment' THEN 'payment'
                ELSE 'status_change'
            END as reason
        FROM filtered_orders
        GROUP BY id
    ),
    -- Возвраты за выбранный период (независимо от даты принятия заказа)
    period_returns AS (
        SELECT
            SUM(r.return_amount) as total_return_amount,
            SUM(COALESCE(r.original_benefit, 0)) as total_return_benefit
        FROM order_returns r
        JOIN orders o ON o.id = r.order_id
        WHERE r.status = 1
        AND r.created_at BETWEEN :startDate AND :endDate
        " . (!$isAdmin ? "AND o.storage_id = :storageId" : "") . "
        " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND o.cashbox_id = :cashbox_id" : "") . "
        " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND o.add_user_id = :seller_id" : "") . "
    ),
    orders_with_returns_other_day AS (
        SELECT DISTINCT o.id
        FROM orders o
        JOIN order_returns r ON r.order_id = o.id
        WHERE r.status = 1
        AND r.created_at BETWEEN :startDate AND :endDate
        AND o.accepted_date NOT BETWEEN :startDate AND :endDate
        " . (!$isAdmin ? "AND o.storage_id = :storageId" : "") . "
        " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND o.cashbox_id = :cashbox_id" : "") . "
        " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND o.add_user_id = :seller_id" : "") . "
    ),
    return_orders AS (
        SELECT
            o.id as order_id,
            r.created_at as return_created_at,
            SUM(r.return_amount) as return_amount,
            SUM(COALESCE(r.original_benefit, 0)) as return_benefit,
            r.order_detail_id
        FROM orders o
        JOIN order_returns r ON r.order_id = o.id
        WHERE r.status = 1
        " . (!$isAdmin ? "AND o.storage_id = :storageId" : "") . "
        " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND o.cashbox_id = :cashbox_id" : "") . "
        " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND o.add_user_id = :seller_id" : "") . "
        GROUP BY o.id, r.created_at, r.order_detail_id
    ),
    order_details AS (
        -- Основные детали заказов
        SELECT
            od.order_id,
            od.id,
            od.size_meter,
            od.price,
            od.sell_price,
            'original' as record_type
        FROM order_detail od
        JOIN filtered_orders_with_reason fo ON fo.id = od.order_id
        LEFT JOIN orders_with_returns_other_day owr ON owr.id = od.order_id
        WHERE owr.id IS NULL

        UNION ALL

        -- Детали возвратов
        SELECT
            od.order_id,
            od.id,
            -od.size_meter as size_meter,
            od.price,
            -od.sell_price as sell_price,
            'return' as record_type
        FROM order_detail od
        JOIN return_orders ro ON ro.order_detail_id = od.id
        WHERE ro.return_created_at BETWEEN :startDate AND :endDate
    ),
    order_totals AS (
        SELECT
            SUM(od.size_meter * od.price) as total_sum, -- Умумий сумма (sum of size_meter * price)
            0 as total_all_price, -- Выручка нархи будет взята из заказов
            0 as total_benefit,
            0 as total_benefit_som,
            0 as total_paid_sum
        FROM order_details od

        UNION ALL

        -- Для расчета выручки и общей суммы оплаты
        SELECT
            0 as total_sum,
            SUM(o.total_sell_price) - COALESCE((SELECT total_return_amount FROM period_returns), 0) as total_all_price, -- Выручка нархи из заказов
            SUM(o.benefit) - COALESCE((SELECT total_return_benefit FROM period_returns), 0) as total_benefit, -- Прибыль из заказов с учетом возвратов за период
            (SUM(o.benefit) - COALESCE((SELECT total_return_benefit FROM period_returns), 0)) * (SELECT course FROM currency_course WHERE currency_id = 2 AND end_date = '9999-12-31' ORDER BY start_date DESC LIMIT 1) as total_benefit_som,
            (SELECT COALESCE(SUM(
                CASE
                    WHEN ph.currency_id = 1 THEN ph.sum * (
                        SELECT course FROM currency_course
                        WHERE currency_id = 2
                        AND ph.created_at >= start_date
                        AND ph.created_at < end_date
                        AND end_date = '9999-12-31'
                        ORDER BY start_date DESC LIMIT 1
                    )
                    ELSE ph.sum
                END
             ), 0)
             FROM pays_history ph
             WHERE ph.created_at BETWEEN :startDate AND :endDate
             AND ph.deleted_at IS NULL
             AND ph.order_id IN (SELECT id FROM filtered_orders)
             " . ($cashbox_id && $cashbox_id !== 'null' && $cashbox_id !== '' ? "AND ph.cashbox_id = :cashbox_id" : "") . "
             " . ($seller_id && $seller_id !== 'null' && $seller_id !== '' ? "AND ph.order_id IN (SELECT id FROM orders WHERE add_user_id = :seller_id)" : "") . "
            ) - COALESCE((SELECT total_return_amount FROM period_returns), 0) as total_paid_sum
        FROM orders o
        INNER JOIN filtered_orders_with_reason fo ON fo.id = o.id
        LEFT JOIN orders_with_returns_other_day owr ON owr.id = o.id
        WHERE owr.id IS NULL
    )
        SELECT
            SUM(total_sum) as total_sum,
            SUM(total_benefit) as total_benefit,
            SUM(total_benefit_som) as total_benefit_som,
            SUM(total_paid_sum) as total_paid_sum,
            SUM(total_all_price) as total_all_price
        FROM order_totals
    ";

    // Получаем суммы по всем записям за весь период, используя параметры без limit и offset
    $footerTotals = Yii::$app->db->createCommand($totalsSql, $params)->queryOne();

    // Округляем полученные значения для отображения
    $footerTotals['total_benefit'] = round(floatval($footerTotals['total_benefit'] ?? 0), 2);  // До 2 знаков
    $footerTotals['total_benefit_som'] = round(floatval($footerTotals['total_benefit_som'] ?? 0)); // До целого
    $footerTotals['total_sum'] = round(floatval($footerTotals['total_sum'] ?? 0)); // До целого
    $footerTotals['total_paid_sum'] = round(floatval($footerTotals['total_paid_sum'] ?? 0)); // До целого
    $footerTotals['total_all_price'] = round(floatval($footerTotals['total_all_price'] ?? 0), 2); // До 2 знаков

    // Кастомное округление: если остаток от деления на 1000 превышает 500 – округляем вверх,
    // иначе – округляем вниз (работает только для неотрицательных значений).
    $benefitSom = floatval($footerTotals['total_benefit_som'] ?? 0);
    $remainder = $benefitSom % 1000;
    $roundedBenefitSom = ($benefitSom - $remainder) + ($remainder > 500 ? 1000 : 0);
    $footerTotals['total_benefit_som'] = (int)$roundedBenefitSom; // До 1000

    // Используем значение benefit из footerTotals вместо отдельного расчета
    $totalCompanyProfit = $footerTotals['total_benefit'] ?? 0;
    $companyProfitSom = $footerTotals['total_benefit_som'] ?? 0;

    // Проверяем на существование данных, чтобы избежать ошибок при работе с данными
    if ($footerTotals === false) {
        $footerTotals = [
            'total_sum' => 0,
            'total_benefit' => 0,
            'total_benefit_som' => 0,
            'total_paid_sum' => 0,
            'total_all_price' => 0
        ];
    }

    // В представлении уже есть инициализация переменных из footerTotals, поэтому не создаем их здесь

    if (Yii::$app->request->isAjax) {
        return $this->renderPartial('index', [
            'detailedOrders' => $detailedOrders,
            'page' => $page,
            'pageSize' => $pageSize,
            'totalCount' => $totalCount,
            'pagination' => [
                'totalCount' => $totalCount,
                'page' => $page,
                'pageSize' => $pageSize,
            ],
            'totalCompanyProfit' => $totalCompanyProfit,
            'companyProfitSom' => $companyProfitSom,
            'footerTotals' => $footerTotals,
        ]);
    }

    return $this->render('index', [
        'detailedOrders' => $detailedOrders,
        'page' => $page,
        'pageSize' => $pageSize,
        'totalCount' => $totalCount,
        'totalCompanyProfit' => $totalCompanyProfit,
        'companyProfitSom' => $companyProfitSom,
        'footerTotals' => $footerTotals,
    ]);
    }
}