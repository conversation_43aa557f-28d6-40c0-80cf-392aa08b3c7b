<?php

use yii\helpers\Html;
use yii\widgets\Pjax;

/* @var $this yii\web\View */
/* @var $serial string */
/* @var $salesHistory array */
/* @var $originalSize float */

$this->title = Yii::t('app', 'serial_sales_history');
$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'Storage'), 'url' => ['storage/remains']];
$this->params['breadcrumbs'][] = ['label' => Yii::t('app', 'product_detail'), 'url' => Yii::$app->request->referrer];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h3 class="mb-0"><?= Yii::t('app', 'serial_sales_history') ?>: <?= Html::encode($serial) ?></h3>
            <p class="text-muted mb-0"><?= Yii::t('app', 'original_size') ?>: <strong><?= Html::encode($originalSize) ?> м²</strong></p>
        </div>
        <div class="col-md-6 d-flex justify-content-end">
            <?= Html::a(Yii::t('app', 'back_'), Yii::$app->request->referrer, ['class' => 'btn btn-primary']) ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'sales-history-pjax']); ?>
    <div class="table-responsive">
        <table class="table table-bordered table-hover" id="sales-history-table">
            <thead>
                <tr>
                    <th><?= Yii::t('app', 'serial_number') ?></th>
                    <th><?= Yii::t('app', 'seller_name') ?></th>
                    <th><?= Yii::t('app', 'size_meter') ?></th>
                    <th><?= Yii::t('app', 'sale_date') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($salesHistory)): ?>
                    <?php foreach ($salesHistory as $sale): ?>
                        <tr>
                            <td><?= Html::encode($sale['serial']) ?></td>
                            <td><?= Html::encode($sale['seller_name']) ?></td>
                            <td><?= Html::encode($sale['size_meter'] . ' м²') ?></td>
                            <td><?= Html::encode(date('d.m.Y H:i', strtotime($sale['created_at']))) ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="4" class="text-center"><?= Yii::t('app', 'No data available.') ?></td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    <?php Pjax::end(); ?>
</div>

<style>
    .table-hover tbody tr:hover {
        background-color: #f5f5f5;
        cursor: pointer;
    }
</style>
